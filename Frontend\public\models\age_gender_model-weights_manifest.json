[{"weights": [{"name": "entry_flow/conv_in/filters", "shape": [3, 3, 3, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.005431825039433498, "min": -0.7441600304023892}}, {"name": "entry_flow/conv_in/bias", "shape": [32], "dtype": "float32"}, {"name": "entry_flow/reduction_block_0/separable_conv0/depthwise_filter", "shape": [3, 3, 32, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.005691980614381678, "min": -0.6090419257388395}}, {"name": "entry_flow/reduction_block_0/separable_conv0/pointwise_filter", "shape": [1, 1, 32, 64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.009089225881239947, "min": -1.1179747833925135}}, {"name": "entry_flow/reduction_block_0/separable_conv0/bias", "shape": [64], "dtype": "float32"}, {"name": "entry_flow/reduction_block_0/separable_conv1/depthwise_filter", "shape": [3, 3, 64, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00683894624897078, "min": -0.8138346036275228}}, {"name": "entry_flow/reduction_block_0/separable_conv1/pointwise_filter", "shape": [1, 1, 64, 64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.011632566358528886, "min": -1.3028474321552352}}, {"name": "entry_flow/reduction_block_0/separable_conv1/bias", "shape": [64], "dtype": "float32"}, {"name": "entry_flow/reduction_block_0/expansion_conv/filters", "shape": [1, 1, 32, 64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010254812240600587, "min": -0.9229331016540528}}, {"name": "entry_flow/reduction_block_0/expansion_conv/bias", "shape": [64], "dtype": "float32"}, {"name": "entry_flow/reduction_block_1/separable_conv0/depthwise_filter", "shape": [3, 3, 64, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0052509616403018725, "min": -0.6406173201168285}}, {"name": "entry_flow/reduction_block_1/separable_conv0/pointwise_filter", "shape": [1, 1, 64, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010788509424994973, "min": -1.4564487723743214}}, {"name": "entry_flow/reduction_block_1/separable_conv0/bias", "shape": [128], "dtype": "float32"}, {"name": "entry_flow/reduction_block_1/separable_conv1/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00553213918910307, "min": -0.7025816770160899}}, {"name": "entry_flow/reduction_block_1/separable_conv1/pointwise_filter", "shape": [1, 1, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.013602388606351965, "min": -1.6186842441558837}}, {"name": "entry_flow/reduction_block_1/separable_conv1/bias", "shape": [128], "dtype": "float32"}, {"name": "entry_flow/reduction_block_1/expansion_conv/filters", "shape": [1, 1, 64, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.007571851038465313, "min": -1.158493208885193}}, {"name": "entry_flow/reduction_block_1/expansion_conv/bias", "shape": [128], "dtype": "float32"}, {"name": "middle_flow/main_block_0/separable_conv0/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.005766328409606335, "min": -0.6688940955143349}}, {"name": "middle_flow/main_block_0/separable_conv0/pointwise_filter", "shape": [1, 1, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.012136116214826995, "min": -1.5776951079275094}}, {"name": "middle_flow/main_block_0/separable_conv0/bias", "shape": [128], "dtype": "float32"}, {"name": "middle_flow/main_block_0/separable_conv1/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.004314773222979377, "min": -0.5652352922102984}}, {"name": "middle_flow/main_block_0/separable_conv1/pointwise_filter", "shape": [1, 1, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.01107162026798024, "min": -1.2400214700137868}}, {"name": "middle_flow/main_block_0/separable_conv1/bias", "shape": [128], "dtype": "float32"}, {"name": "middle_flow/main_block_0/separable_conv2/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0036451735917259667, "min": -0.4848080876995536}}, {"name": "middle_flow/main_block_0/separable_conv2/pointwise_filter", "shape": [1, 1, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.008791744942758598, "min": -1.134135097615859}}, {"name": "middle_flow/main_block_0/separable_conv2/bias", "shape": [128], "dtype": "float32"}, {"name": "middle_flow/main_block_1/separable_conv0/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.004915751896652521, "min": -0.6095532351849126}}, {"name": "middle_flow/main_block_1/separable_conv0/pointwise_filter", "shape": [1, 1, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010868691463096469, "min": -1.3368490499608656}}, {"name": "middle_flow/main_block_1/separable_conv0/bias", "shape": [128], "dtype": "float32"}, {"name": "middle_flow/main_block_1/separable_conv1/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.005010117269029804, "min": -0.6012140722835765}}, {"name": "middle_flow/main_block_1/separable_conv1/pointwise_filter", "shape": [1, 1, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010311148213405235, "min": -1.3816938605963016}}, {"name": "middle_flow/main_block_1/separable_conv1/bias", "shape": [128], "dtype": "float32"}, {"name": "middle_flow/main_block_1/separable_conv2/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.004911523706772748, "min": -0.7367285560159123}}, {"name": "middle_flow/main_block_1/separable_conv2/pointwise_filter", "shape": [1, 1, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.008976466047997568, "min": -1.2207993825276693}}, {"name": "middle_flow/main_block_1/separable_conv2/bias", "shape": [128], "dtype": "float32"}, {"name": "exit_flow/reduction_block/separable_conv0/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.005074804436926748, "min": -0.7104726211697447}}, {"name": "exit_flow/reduction_block/separable_conv0/pointwise_filter", "shape": [1, 1, 128, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.011453078307357489, "min": -1.4545409450344011}}, {"name": "exit_flow/reduction_block/separable_conv0/bias", "shape": [256], "dtype": "float32"}, {"name": "exit_flow/reduction_block/separable_conv1/depthwise_filter", "shape": [3, 3, 256, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.007741751390344957, "min": -1.1380374543807086}}, {"name": "exit_flow/reduction_block/separable_conv1/pointwise_filter", "shape": [1, 1, 256, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.011347713189966538, "min": -1.497898141075583}}, {"name": "exit_flow/reduction_block/separable_conv1/bias", "shape": [256], "dtype": "float32"}, {"name": "exit_flow/reduction_block/expansion_conv/filters", "shape": [1, 1, 128, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.006717281014311547, "min": -0.8329428457746318}}, {"name": "exit_flow/reduction_block/expansion_conv/bias", "shape": [256], "dtype": "float32"}, {"name": "exit_flow/separable_conv/depthwise_filter", "shape": [3, 3, 256, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0027201742518181892, "min": -0.3237007359663645}}, {"name": "exit_flow/separable_conv/pointwise_filter", "shape": [1, 1, 256, 512], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010076364348916447, "min": -1.330080094056971}}, {"name": "exit_flow/separable_conv/bias", "shape": [512], "dtype": "float32"}, {"name": "fc/age/weights", "shape": [512, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.008674054987290326, "min": -1.2664120281443876}}, {"name": "fc/age/bias", "shape": [1], "dtype": "float32"}, {"name": "fc/gender/weights", "shape": [512, 2], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0029948226377075793, "min": -0.34140978069866407}}, {"name": "fc/gender/bias", "shape": [2], "dtype": "float32"}], "paths": ["age_gender_model-shard1"]}]